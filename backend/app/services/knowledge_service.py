"""
知识点管理服务类
"""

from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, text

from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from app.models.mapping import ItemKpMap
from app.schemas.knowledge import KnowledgePointCreate, KnowledgePointUpdate, KnowledgePointTreeNode


class KnowledgeService:
    """知识点服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get(self, kp_id: int) -> Optional[KnowledgePoint]:
        """根据ID获取知识点"""
        return self.db.query(KnowledgePoint).filter(
            KnowledgePoint.kp_id == kp_id
        ).first()
    
    def get_by_code(self, code: str) -> Optional[KnowledgePoint]:
        """根据编码获取知识点"""
        return self.db.query(KnowledgePoint).filter(
            KnowledgePoint.code == code
        ).first()
    
    def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[KnowledgePoint]:
        """获取知识点列表"""
        query = self.db.query(KnowledgePoint)
        
        # 搜索条件
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    KnowledgePoint.name.ilike(search_term),
                    KnowledgePoint.code.ilike(search_term),
                    KnowledgePoint.description.ilike(search_term)
                )
            )
        
        # 过滤条件
        if filters:
            if 'subject' in filters:
                # 假设学科信息存储在path的第一级
                query = query.filter(KnowledgePoint.path.like(f"{filters['subject']}.%"))
            
            if 'parent_id' in filters:
                if filters['parent_id'] == 'null':
                    query = query.filter(KnowledgePoint.parent_id.is_(None))
                else:
                    query = query.filter(KnowledgePoint.parent_id == int(filters['parent_id']))
            
            if 'is_leaf' in filters:
                query = query.filter(KnowledgePoint.is_leaf == bool(filters['is_leaf']))
            
            if 'difficulty_level' in filters:
                query = query.filter(KnowledgePoint.difficulty_level == int(filters['difficulty_level']))
        
        return query.offset(skip).limit(limit).all()
    
    def count(
        self, 
        search: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """统计知识点数量"""
        query = self.db.query(func.count(KnowledgePoint.kp_id))
        
        # 搜索条件
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    KnowledgePoint.name.ilike(search_term),
                    KnowledgePoint.code.ilike(search_term),
                    KnowledgePoint.description.ilike(search_term)
                )
            )
        
        # 过滤条件
        if filters:
            if 'subject' in filters:
                query = query.filter(KnowledgePoint.path.like(f"{filters['subject']}.%"))
            
            if 'parent_id' in filters:
                if filters['parent_id'] == 'null':
                    query = query.filter(KnowledgePoint.parent_id.is_(None))
                else:
                    query = query.filter(KnowledgePoint.parent_id == int(filters['parent_id']))
            
            if 'is_leaf' in filters:
                query = query.filter(KnowledgePoint.is_leaf == bool(filters['is_leaf']))
            
            if 'difficulty_level' in filters:
                query = query.filter(KnowledgePoint.difficulty_level == int(filters['difficulty_level']))
        
        return query.scalar()
    
    def create(self, kp_in: KnowledgePointCreate, creator_id: int) -> KnowledgePoint:
        """创建知识点"""
        kp_data = kp_in.model_dump()
        kp_data['created_by'] = creator_id
        kp_data['updated_by'] = creator_id
        
        # 生成路径
        if kp_data.get('parent_id'):
            parent = self.get(kp_data['parent_id'])
            if parent:
                kp_data['path'] = f"{parent.path}.{kp_data['code']}"
            else:
                raise ValueError("父知识点不存在")
        else:
            kp_data['path'] = kp_data['code']
        
        # 默认为叶子节点
        kp_data['is_leaf'] = True
        
        kp = KnowledgePoint(**kp_data)
        self.db.add(kp)
        self.db.flush()  # 获取ID但不提交
        
        # 如果有父节点，更新父节点的is_leaf状态
        if kp_data.get('parent_id'):
            parent = self.get(kp_data['parent_id'])
            if parent:
                parent.is_leaf = False
        
        self.db.commit()
        self.db.refresh(kp)
        return kp
    
    def update(self, kp: KnowledgePoint, kp_in: KnowledgePointUpdate) -> KnowledgePoint:
        """更新知识点"""
        update_data = kp_in.model_dump(exclude_unset=True)
        
        # 如果更新了父节点，需要重新计算路径
        if 'parent_id' in update_data:
            old_parent_id = kp.parent_id
            new_parent_id = update_data['parent_id']
            
            if new_parent_id != old_parent_id:
                # 更新路径
                if new_parent_id:
                    parent = self.get(new_parent_id)
                    if parent:
                        update_data['path'] = f"{parent.path}.{kp.code}"
                    else:
                        raise ValueError("父知识点不存在")
                else:
                    update_data['path'] = kp.code
                
                # 更新所有子节点的路径
                self._update_children_paths(kp.kp_id, update_data['path'])
                
                # 更新旧父节点的is_leaf状态
                if old_parent_id:
                    old_parent = self.get(old_parent_id)
                    if old_parent and not self.has_children(old_parent_id):
                        old_parent.is_leaf = True
                
                # 更新新父节点的is_leaf状态
                if new_parent_id:
                    new_parent = self.get(new_parent_id)
                    if new_parent:
                        new_parent.is_leaf = False
        
        for field, value in update_data.items():
            setattr(kp, field, value)
        
        self.db.commit()
        self.db.refresh(kp)
        return kp
    
    def remove(self, kp_id: int) -> bool:
        """删除知识点"""
        kp = self.get(kp_id)
        if kp:
            # 更新父节点的is_leaf状态
            if kp.parent_id:
                parent = self.get(kp.parent_id)
                if parent and not self.has_children(kp.parent_id, exclude_id=kp_id):
                    parent.is_leaf = True
            
            self.db.delete(kp)
            self.db.commit()
            return True
        return False
    
    def get_tree(self, subject: Optional[str] = None) -> List[KnowledgePointTreeNode]:
        """获取知识点树形结构"""
        # 获取根节点
        query = self.db.query(KnowledgePoint).filter(KnowledgePoint.parent_id.is_(None))
        
        if subject:
            query = query.filter(KnowledgePoint.path.like(f"{subject}%"))
        
        root_nodes = query.all()
        
        # 构建树形结构
        tree = []
        for node in root_nodes:
            tree_node = self._build_tree_node(node)
            tree.append(tree_node)
        
        return tree
    
    def _build_tree_node(self, kp: KnowledgePoint) -> KnowledgePointTreeNode:
        """构建树节点"""
        children = self.db.query(KnowledgePoint).filter(
            KnowledgePoint.parent_id == kp.kp_id
        ).all()
        
        child_nodes = []
        for child in children:
            child_node = self._build_tree_node(child)
            child_nodes.append(child_node)
        
        return KnowledgePointTreeNode(
            kp_id=kp.kp_id,
            name=kp.name,
            code=kp.code,
            description=kp.description,
            difficulty_level=kp.difficulty_level,
            is_leaf=kp.is_leaf,
            children=child_nodes
        )
    
    def get_children(self, kp_id: int) -> List[KnowledgePoint]:
        """获取子知识点"""
        return self.db.query(KnowledgePoint).filter(
            KnowledgePoint.parent_id == kp_id
        ).all()
    
    def get_prerequisites(self, kp_id: int) -> List[Dict[str, Any]]:
        """获取先修知识点"""
        prerequisites = self.db.query(PrerequisiteRelation).options(
            joinedload(PrerequisiteRelation.pre_kp)
        ).filter(PrerequisiteRelation.post_kp_id == kp_id).all()
        
        result = []
        for prereq in prerequisites:
            result.append({
                'kp_id': prereq.pre_kp_id,
                'name': prereq.pre_kp.name,
                'code': prereq.pre_kp.code,
                'source': prereq.source,
                'confidence': prereq.confidence
            })
        
        return result
    
    def has_children(self, kp_id: int, exclude_id: Optional[int] = None) -> bool:
        """检查是否有子知识点"""
        query = self.db.query(func.count(KnowledgePoint.kp_id)).filter(
            KnowledgePoint.parent_id == kp_id
        )
        
        if exclude_id:
            query = query.filter(KnowledgePoint.kp_id != exclude_id)
        
        return query.scalar() > 0
    
    def has_linked_questions(self, kp_id: int) -> bool:
        """检查是否有关联的题目"""
        count = self.db.query(func.count(ItemKpMap.question_id)).filter(
            ItemKpMap.kp_id == kp_id
        ).scalar()
        
        return count > 0
    
    def would_create_cycle(self, kp_id: int, parent_id: int) -> bool:
        """检查是否会形成循环"""
        # 检查parent_id是否是kp_id的后代
        current_id = parent_id
        visited = set()
        
        while current_id and current_id not in visited:
            if current_id == kp_id:
                return True
            
            visited.add(current_id)
            parent = self.get(current_id)
            current_id = parent.parent_id if parent else None
        
        return False
    
    def _update_children_paths(self, kp_id: int, new_path: str):
        """更新子节点路径"""
        children = self.get_children(kp_id)
        for child in children:
            child.path = f"{new_path}.{child.code}"
            self._update_children_paths(child.kp_id, child.path)

    def add_prerequisite(self, kp_id: int, prereq_id: int, creator_id: int) -> bool:
        """添加先修关系"""
        # 检查是否已存在
        existing = self.db.query(PrerequisiteRelation).filter(
            PrerequisiteRelation.pre_kp_id == prereq_id,
            PrerequisiteRelation.post_kp_id == kp_id
        ).first()

        if existing:
            return False

        # 创建新的先修关系
        prereq_relation = PrerequisiteRelation(
            pre_kp_id=prereq_id,
            post_kp_id=kp_id,
            created_by=creator_id,
            updated_by=creator_id
        )

        self.db.add(prereq_relation)
        self.db.commit()
        return True

    def remove_prerequisite(self, kp_id: int, prereq_id: int) -> bool:
        """移除先修关系"""
        prereq_relation = self.db.query(PrerequisiteRelation).filter(
            PrerequisiteRelation.pre_kp_id == prereq_id,
            PrerequisiteRelation.post_kp_id == kp_id
        ).first()

        if prereq_relation:
            self.db.delete(prereq_relation)
            self.db.commit()
            return True

        return False

    def would_create_prerequisite_cycle(self, kp_id: int, prereq_id: int) -> bool:
        """检查添加先修关系是否会形成环路"""
        # 使用递归查询检查是否会形成环路
        # 从prereq_id开始，沿着先修关系向前查找，看是否能到达kp_id

        visited = set()
        stack = [prereq_id]

        while stack:
            current_id = stack.pop()

            if current_id == kp_id:
                return True

            if current_id in visited:
                continue

            visited.add(current_id)

            # 获取current_id的所有先修知识点
            prerequisites = self.db.query(PrerequisiteRelation.pre_kp_id).filter(
                PrerequisiteRelation.post_kp_id == current_id
            ).all()

            for prereq in prerequisites:
                if prereq.pre_kp_id not in visited:
                    stack.append(prereq.pre_kp_id)

        return False

    def get_transitive_closure(self, kp_ids: Optional[List[int]] = None) -> Dict[int, List[int]]:
        """计算先修关系的传递闭包"""
        # 如果没有指定知识点，获取所有知识点
        if kp_ids is None:
            kp_ids = [kp.kp_id for kp in self.db.query(KnowledgePoint.kp_id).all()]

        # 初始化闭包字典
        closure = {kp_id: set() for kp_id in kp_ids}

        # 获取所有直接先修关系
        relations = self.db.query(PrerequisiteRelation).filter(
            and_(
                PrerequisiteRelation.pre_kp_id.in_(kp_ids),
                PrerequisiteRelation.post_kp_id.in_(kp_ids)
            )
        ).all()

        # 添加直接先修关系
        for relation in relations:
            closure[relation.post_kp_id].add(relation.pre_kp_id)

        # 计算传递闭包（Floyd-Warshall算法）
        for k in kp_ids:
            for i in kp_ids:
                for j in kp_ids:
                    if k in closure[i] and j in closure[k]:
                        closure[i].add(j)

        # 转换为列表格式
        return {kp_id: list(prereqs) for kp_id, prereqs in closure.items()}

    def get_all_prerequisites(self, kp_id: int) -> List[int]:
        """获取知识点的所有先修知识点（包括间接先修）"""
        closure = self.get_transitive_closure([kp_id])
        return closure.get(kp_id, [])
